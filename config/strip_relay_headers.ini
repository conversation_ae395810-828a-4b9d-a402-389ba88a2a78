[main]
; Enable or disable header stripping
enabled=true

; Hostname of your home server (for IP resolution)
relay_hostname=hallocktest6.tmv.co.il

; Your domain name (used in replacement headers)
relay_domain=mosh.wtf

; Strip Received headers that contain your home IP
strip_received_headers=true

; Strip X-Originating-IP headers
strip_x_originating_ip=true

; Strip X-Forwarded-For headers
strip_x_forwarded_for=true

; Strip X-Real-IP headers
strip_x_real_ip=true

; Strip custom headers defined in [headers_to_strip] section
strip_custom_headers=true

; Add clean replacement Received headers
replace_received_headers=true

[headers_to_strip]
; Add any additional headers you want to strip
; Format: header_name=1
; Example:
; X-Source-IP=1
; X-Client-IP=1
; X-Remote-IP=1

[logging]
; Log level for this plugin (debug, info, warn, error)
level=info
