#!/usr/bin/env node

// Test script for the header stripping plugin
const dns = require('node:dns');
const fs = require('node:fs');
const path = require('node:path');

const hostname = 'hallocktest6.tmv.co.il';
const config_dir = './config';

console.log('Testing Header Stripping Plugin functionality...\n');

// Test DNS resolution
console.log(`1. Testing DNS resolution for ${hostname}:`);
dns.resolve4(hostname, (err, addresses) => {
    if (err) {
        console.error(`   ❌ DNS resolution failed: ${err.message}`);
        return;
    }
    
    if (!addresses || addresses.length === 0) {
        console.error(`   ❌ No IP addresses found for ${hostname}`);
        return;
    }
    
    const ip = addresses[0];
    console.log(`   ✅ Resolved ${hostname} to: ${ip}`);
    
    // Test plugin configuration
    console.log('\n2. Testing plugin configuration:');
    const config_file = path.join(config_dir, 'strip_relay_headers.ini');
    fs.readFile(config_file, 'utf8', (err, data) => {
        if (err) {
            console.error(`   ❌ Could not read ${config_file}: ${err.message}`);
        } else {
            console.log(`   ✅ Plugin configuration file exists`);
            console.log('   Configuration preview:');
            const lines = data.split('\n').slice(0, 15);
            lines.forEach(line => console.log(`     ${line}`));
        }
        
        console.log('\n3. Testing plugin file:');
        const plugin_file = path.join('./plugins', 'strip_relay_headers.js');
        fs.access(plugin_file, fs.constants.F_OK, (err) => {
            if (err) {
                console.error(`   ❌ Plugin file not found: ${plugin_file}`);
            } else {
                console.log(`   ✅ Plugin file exists: ${plugin_file}`);
            }
            
            console.log('\n4. Testing plugins configuration:');
            const plugins_file = path.join(config_dir, 'plugins');
            fs.readFile(plugins_file, 'utf8', (err, data) => {
                if (err) {
                    console.error(`   ❌ Could not read ${plugins_file}: ${err.message}`);
                } else {
                    if (data.includes('strip_relay_headers')) {
                        console.log(`   ✅ Plugin is enabled in plugins configuration`);
                    } else {
                        console.log(`   ⚠️  Plugin is NOT enabled in plugins configuration`);
                    }
                }
                
                console.log('\n5. Testing header patterns:');
                testHeaderPatterns(ip);
            });
        });
    });
});

function testHeaderPatterns(ip) {
    console.log(`   Testing with IP: ${ip}`);
    
    // Sample headers that should be stripped
    const test_headers = [
        `Received: from [${ip}] (helo=hallocktest6.tmv.co.il) by mosh.wtf`,
        `X-Originating-IP: ${ip}`,
        `X-Forwarded-For: ${ip}`,
        `X-Real-IP: ${ip}`,
        `Received: from hallocktest6.tmv.co.il (${ip}) by relay.example.com`,
        `Received: from ************* by hallocktest6.tmv.co.il`,
        `X-Source-IP: *********`
    ];
    
    console.log('   Headers that should be stripped:');
    test_headers.forEach((header, index) => {
        const should_strip = shouldStripHeader(header, ip);
        const status = should_strip ? '✅ STRIP' : '❌ KEEP';
        console.log(`     ${status}: ${header}`);
    });
    
    // Sample headers that should be kept
    const keep_headers = [
        'From: <EMAIL>',
        'To: <EMAIL>',
        'Subject: Test Message',
        'Date: Mon, 20 Jul 2025 10:00:00 +0000',
        'Message-ID: <<EMAIL>>',
        'Received: from external.server.com by mosh.wtf'
    ];
    
    console.log('\n   Headers that should be kept:');
    keep_headers.forEach((header, index) => {
        const should_strip = shouldStripHeader(header, ip);
        const status = should_strip ? '❌ STRIP' : '✅ KEEP';
        console.log(`     ${status}: ${header}`);
    });
    
    console.log('\n✅ Header pattern test completed!');
    console.log('\nTo start using the plugin:');
    console.log('1. Restart Haraka to load the new plugin');
    console.log('2. Send a test email from your home server');
    console.log('3. Check the received email headers to verify IP stripping');
    console.log('4. Monitor Haraka logs for plugin activity');
}

function shouldStripHeader(header, relay_ip) {
    const header_lower = header.toLowerCase();
    
    // Check for relay IP
    if (header.includes(relay_ip)) return true;
    
    // Check for hostname
    if (header.includes('hallocktest6.tmv.co.il')) return true;
    
    // Check for private IPs
    const private_ip_patterns = [
        /\b192\.168\.\d+\.\d+\b/,
        /\b10\.\d+\.\d+\.\d+\b/,
        /\b172\.(1[6-9]|2[0-9]|3[01])\.\d+\.\d+\b/
    ];
    
    for (const pattern of private_ip_patterns) {
        if (pattern.test(header)) return true;
    }
    
    // Check for specific header types that commonly contain IPs
    if (header_lower.startsWith('x-originating-ip:')) return true;
    if (header_lower.startsWith('x-forwarded-for:')) return true;
    if (header_lower.startsWith('x-real-ip:')) return true;
    if (header_lower.startsWith('x-source-ip:')) return true;
    
    return false;
}
