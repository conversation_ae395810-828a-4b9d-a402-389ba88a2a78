# Header Stripping Plugin for Haraka

This plugin automatically removes email headers that reveal your home server's IP address when relaying mail through your Haraka server. It ensures that recipients cannot see your actual home IP address, providing better privacy and security.

## Features

- **Automatic header detection**: Identifies headers containing your home server IP
- **Multiple header types**: Strips Received, X-Originating-IP, X-Forwarded-For, X-Real-IP, and custom headers
- **Private IP detection**: Removes headers containing private IP addresses
- **Hostname filtering**: Strips headers mentioning your home server hostname
- **Clean replacement headers**: Optionally adds clean Received headers
- **Configurable**: Fully customizable through configuration file
- **Relay-only processing**: Only processes mail being relayed (not incoming mail)

## Installation

The plugin is already installed and configured in your Haraka setup:

1. **Plugin file**: `plugins/strip_relay_headers.js`
2. **Configuration**: `config/strip_relay_headers.ini`
3. **Enabled in**: `config/plugins` (in the DATA section)

## How It Works

When an email is relayed through your Haraka server from `hallocktest6.tmv.co.il`, the plugin:

1. **Resolves your home IP**: Automatically gets the current IP of `hallocktest6.tmv.co.il`
2. **Scans headers**: Examines all email headers for IP-revealing information
3. **Removes problematic headers**: Strips headers containing:
   - Your current home IP address
   - Your hostname (`hallocktest6.tmv.co.il`)
   - Private IP addresses (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
   - Specific header types (X-Originating-IP, X-Forwarded-For, etc.)
4. **Adds clean headers**: Optionally adds replacement Received headers
5. **Logs activity**: Records all header modifications

## Configuration

Edit `config/strip_relay_headers.ini` to customize behavior:

```ini
[main]
; Enable or disable header stripping
enabled=true

; Hostname of your home server (for IP resolution)
relay_hostname=hallocktest6.tmv.co.il

; Your domain name (used in replacement headers)
relay_domain=mosh.wtf

; Strip Received headers that contain your home IP
strip_received_headers=true

; Strip X-Originating-IP headers
strip_x_originating_ip=true

; Strip X-Forwarded-For headers
strip_x_forwarded_for=true

; Strip X-Real-IP headers
strip_x_real_ip=true

; Strip custom headers defined in [headers_to_strip] section
strip_custom_headers=true

; Add clean replacement Received headers
replace_received_headers=true

[headers_to_strip]
; Add any additional headers you want to strip
; X-Source-IP=1
; X-Client-IP=1
; X-Remote-IP=1
```

## Headers That Are Stripped

### Automatic Detection
- **Received headers** containing your home IP or hostname
- **X-Originating-IP** headers with your IP
- **X-Forwarded-For** headers with your IP
- **X-Real-IP** headers with your IP
- **Any header** containing private IP addresses
- **Any header** mentioning `hallocktest6.tmv.co.il`

### Custom Headers
You can configure additional headers to strip in the `[headers_to_strip]` section.

## Headers That Are Preserved

The plugin only removes headers that reveal your home server information. All other headers are preserved:

- From, To, Subject, Date
- Message-ID, Content-Type, MIME headers
- DKIM signatures
- SPF/DMARC related headers
- Received headers from other servers (not containing your IP)

## Testing

Test the plugin setup:

```bash
./test_header_stripping.js
```

This will verify:
- DNS resolution of your hostname
- Plugin configuration
- Header pattern matching
- Plugin registration

## Usage Examples

### Before Header Stripping
```
Received: from [*************] (helo=hallocktest6.tmv.co.il) 
    by mosh.wtf with ESMTP
X-Originating-IP: *************
From: <EMAIL>
To: <EMAIL>
Subject: Test Message
```

### After Header Stripping
```
Received: from mosh.wtf (mosh.wtf) by mosh.wtf with ESMTP; 
    Mon, 20 Jul 2025 10:00:00 GMT
From: <EMAIL>
To: <EMAIL>
Subject: Test Message
```

## Starting the Plugin

1. **Restart Haraka** to load the plugin:
   ```bash
   sudo systemctl restart haraka
   # or however you normally restart Haraka
   ```

2. **Monitor the logs** for plugin activity:
   ```bash
   tail -f /var/log/haraka.log
   # Look for "Strip Relay Headers" messages
   ```

3. **Send a test email** from your home server and check the headers

## Log Messages

The plugin generates informative log messages:

```
[INFO] Removing header: received (contains relay IP)
[INFO] Removing header: x-originating-ip (X-Originating-IP with relay IP)
[INFO] Stripped 2 headers revealing relay IP
[DEBUG] Added clean Received header: from mosh.wtf...
```

## Troubleshooting

### Plugin Not Working
- Verify the plugin is listed in `config/plugins`
- Check that the plugin file exists and has no syntax errors
- Ensure Haraka was restarted after installation

### Headers Still Visible
- Check that the email is actually being relayed (not direct delivery)
- Verify your home IP is correctly resolved
- Review the configuration settings
- Check logs for any error messages

### DNS Resolution Issues
- Ensure `hallocktest6.tmv.co.il` resolves correctly
- Check network connectivity from the Haraka server
- Verify DNS server configuration

## Security Benefits

This plugin provides several security and privacy benefits:

1. **IP Privacy**: Recipients cannot see your home IP address
2. **Location Privacy**: Harder to determine your physical location
3. **Network Security**: Internal network topology is hidden
4. **Professional Appearance**: Emails appear to come directly from your domain

## Performance Impact

The plugin has minimal performance impact:
- Only processes relayed mail (not all mail)
- Simple string matching operations
- No external network calls during processing
- DNS resolution happens asynchronously

## Integration with Dynamic IP Updater

This plugin works seamlessly with the Dynamic IP Updater plugin:
- Automatically gets the current IP when it changes
- No manual configuration needed when IP updates
- Both plugins work independently

## Files Created/Modified

- `plugins/strip_relay_headers.js` - Main plugin file
- `config/strip_relay_headers.ini` - Plugin configuration
- `config/plugins` - Updated to include the plugin
- `test_header_stripping.js` - Test script

## Advanced Configuration

### Custom Header Patterns
Add specific headers to strip in the configuration:

```ini
[headers_to_strip]
X-Source-IP=1
X-Client-IP=1
X-Remote-Addr=1
X-Forwarded-Host=1
```

### Replacement Headers
Customize the replacement Received header format by modifying the `add_clean_received_header` function in the plugin.

### Logging Levels
Adjust logging verbosity in the configuration:

```ini
[logging]
level=debug  # debug, info, warn, error
```
