#!/bin/bash

# Docker MongoDB Setup Script for Haraka
# This script sets up MongoDB logging in a Docker environment

echo "🐳 Docker MongoDB Setup for Haraka"
echo "=================================="

# Check if we're running in Docker
if [ -f /.dockerenv ]; then
    echo "✅ Running inside Docker container"
    DOCKER_MODE=true
else
    echo "ℹ️  Not running in Docker, will provide Docker commands"
    DOCKER_MODE=false
fi

# Function to install MongoDB driver
install_mongodb_driver() {
    echo "📦 Installing MongoDB Node.js driver..."
    
    if [ "$DOCKER_MODE" = true ]; then
        # We're inside the container
        npm install mongodb
        if [ $? -eq 0 ]; then
            echo "✅ MongoDB driver installed successfully"
            return 0
        else
            echo "❌ Failed to install MongoDB driver"
            return 1
        fi
    else
        # We're outside the container
        echo "Run this command to install MongoDB driver in your container:"
        echo "docker exec -it <haraka_container_name> npm install mongodb"
        return 0
    fi
}

# Function to enable MongoDB logging plugin
enable_mongodb_plugin() {
    echo "🔌 Enabling MongoDB logging plugin..."
    
    local plugins_file="/etc/haraka/config/plugins"
    if [ ! -f "$plugins_file" ]; then
        plugins_file="./config/plugins"
    fi
    
    if [ -f "$plugins_file" ]; then
        # Check if plugin is commented out
        if grep -q "# mongodb_logger" "$plugins_file"; then
            # Uncomment the plugin
            sed -i 's/# mongodb_logger/mongodb_logger/' "$plugins_file"
            echo "✅ MongoDB logger plugin enabled"
        elif grep -q "mongodb_logger" "$plugins_file"; then
            echo "✅ MongoDB logger plugin already enabled"
        else
            # Add the plugin
            echo "mongodb_logger" >> "$plugins_file"
            echo "✅ MongoDB logger plugin added"
        fi
    else
        echo "❌ Plugins file not found: $plugins_file"
        return 1
    fi
}

# Function to test MongoDB connection
test_mongodb_connection() {
    echo "🔍 Testing MongoDB connection..."
    
    # Create a simple test script
    cat > /tmp/test_mongodb.js << 'EOF'
const { MongoClient } = require('mongodb');

async function testConnection() {
    try {
        const client = await MongoClient.connect('mongodb://localhost:27017/haraka_email_logs', {
            connectTimeoutMS: 5000,
            serverSelectionTimeoutMS: 5000
        });
        console.log('✅ MongoDB connection successful');
        await client.close();
        process.exit(0);
    } catch (error) {
        console.log('❌ MongoDB connection failed:', error.message);
        console.log('💡 Make sure MongoDB is running and accessible');
        process.exit(1);
    }
}

testConnection();
EOF

    # Run the test
    if [ "$DOCKER_MODE" = true ]; then
        node /tmp/test_mongodb.js
        rm -f /tmp/test_mongodb.js
    else
        echo "Run this command to test MongoDB connection in your container:"
        echo "docker exec -it <haraka_container_name> node /tmp/test_mongodb.js"
    fi
}

# Function to setup MongoDB database
setup_mongodb_database() {
    echo "🗄️  Setting up MongoDB database..."
    
    local schema_file="/etc/haraka/mongodb_schema.js"
    if [ ! -f "$schema_file" ]; then
        schema_file="./mongodb_schema.js"
    fi
    
    if [ -f "$schema_file" ]; then
        if [ "$DOCKER_MODE" = true ]; then
            node "$schema_file"
            if [ $? -eq 0 ]; then
                echo "✅ MongoDB database setup completed"
            else
                echo "❌ MongoDB database setup failed"
                return 1
            fi
        else
            echo "Run this command to setup MongoDB database in your container:"
            echo "docker exec -it <haraka_container_name> node $schema_file"
        fi
    else
        echo "❌ MongoDB schema file not found: $schema_file"
        return 1
    fi
}

# Main execution
main() {
    echo ""
    echo "Starting MongoDB setup process..."
    echo ""
    
    # Step 1: Install MongoDB driver
    if ! install_mongodb_driver; then
        echo "❌ Setup failed at MongoDB driver installation"
        exit 1
    fi
    
    echo ""
    
    # Step 2: Test MongoDB connection
    test_mongodb_connection
    
    echo ""
    
    # Step 3: Setup database (only if MongoDB is accessible)
    if [ $? -eq 0 ]; then
        setup_mongodb_database
    else
        echo "⚠️  Skipping database setup due to connection issues"
    fi
    
    echo ""
    
    # Step 4: Enable plugin
    enable_mongodb_plugin
    
    echo ""
    echo "🎉 MongoDB setup completed!"
    echo ""
    echo "Next steps:"
    if [ "$DOCKER_MODE" = true ]; then
        echo "1. Restart Haraka to load the MongoDB plugin"
        echo "2. Send test emails to verify logging"
        echo "3. Check logs: grep 'MongoDB Logger' /var/log/haraka.log"
    else
        echo "1. Restart your Haraka Docker container"
        echo "2. Send test emails to verify logging"
        echo "3. Check logs in your container"
    fi
    echo ""
}

# Check command line arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Docker MongoDB Setup Script for Haraka"
    echo ""
    echo "Usage:"
    echo "  $0                    # Run setup (auto-detects Docker)"
    echo "  $0 --help           # Show this help"
    echo ""
    echo "This script:"
    echo "- Installs MongoDB Node.js driver"
    echo "- Tests MongoDB connection"
    echo "- Sets up database schema"
    echo "- Enables MongoDB logging plugin"
    echo ""
    echo "For Docker usage:"
    echo "- Run inside container: docker exec -it <container> $0"
    echo "- Or copy script to container and run"
    exit 0
fi

# Run main function
main
