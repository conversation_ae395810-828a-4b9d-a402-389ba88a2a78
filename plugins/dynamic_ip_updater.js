// Dynamic IP Updater Plugin for Haraka - Minimal Version
// Automatically updates relay_acl_allow when dynamic IP changes

exports.register = function () {
    this.loginfo('Dynamic IP Updater plugin loading...');

    // Register hooks
    this.register_hook('init_master', 'init_master');

    this.loginfo('Dynamic IP Updater plugin registered successfully');
}

exports.init_master = function (next) {
    this.loginfo('Dynamic IP Updater plugin initialized in master process');
    next();
}






