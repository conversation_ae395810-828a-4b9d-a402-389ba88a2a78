// Aggressive IP Stripper Plugin for Haraka
// Strips ANY header containing the home server IP address

const dns = require('dns');

exports.register = function () {
    const plugin = this;
    
    plugin.loginfo('Aggressive IP Stripper plugin loading...');
    
    // Get the home server IP
    plugin.home_ip = null;
    plugin.get_home_ip();
    
    // Register hooks at multiple points to catch all headers
    plugin.register_hook('connect', 'log_connection');
    plugin.register_hook('data_post', 'strip_ip_headers');
    plugin.register_hook('queue', 'strip_ip_headers');
    plugin.register_hook('queue_outbound', 'strip_ip_headers');
    
    plugin.loginfo('Aggressive IP Stripper plugin registered successfully');
}

exports.get_home_ip = function () {
    const plugin = this;
    const hostname = 'hallocktest6.tmv.co.il';
    
    dns.resolve4(hostname, function(err, addresses) {
        if (err) {
            plugin.logwarn('Could not resolve ' + hostname + ': ' + err.message);
            return;
        }
        
        if (addresses && addresses.length > 0) {
            plugin.home_ip = addresses[0];
            plugin.loginfo('Home server IP resolved to: ' + plugin.home_ip);
        }
    });
}

exports.log_connection = function (next, connection) {
    const remote_ip = connection.remote?.ip;
    this.loginfo('Connection from: ' + remote_ip + ' (Home IP: ' + this.home_ip + ')');
    next();
}

exports.strip_ip_headers = function (next, connection) {
    const plugin = this;
    const txn = connection?.transaction;
    
    if (!txn || !plugin.home_ip) {
        return next();
    }
    
    plugin.loginfo('=== AGGRESSIVE IP STRIPPER RUNNING ===');
    plugin.loginfo('Home IP to strip: ' + plugin.home_ip);
    
    const header_obj = txn.header;
    if (!header_obj) {
        plugin.logwarn('No header object found');
        return next();
    }
    
    let headers_stripped = 0;
    
    // Get all headers using different methods
    let all_headers = [];
    
    // Method 1: Try to get headers directly
    if (header_obj.headers) {
        for (const name in header_obj.headers) {
            const values = header_obj.headers[name];
            if (Array.isArray(values)) {
                for (const value of values) {
                    all_headers.push({name: name, value: value});
                }
            } else {
                all_headers.push({name: name, value: values});
            }
        }
    }
    
    // Method 2: Try header_list if available
    if (header_obj.header_list && header_obj.header_list.length > 0) {
        for (const header of header_obj.header_list) {
            if (header.name && header.value) {
                all_headers.push({name: header.name, value: header.value});
            }
        }
    }
    
    plugin.loginfo('Found ' + all_headers.length + ' headers to check');
    
    // Check each header for our IP
    for (const header of all_headers) {
        if (!header.value || typeof header.value !== 'string') continue;
        
        if (header.value.includes(plugin.home_ip)) {
            plugin.loginfo('STRIPPING HEADER: ' + header.name + ' (contains ' + plugin.home_ip + ')');
            plugin.loginfo('Header value: ' + header.value.substring(0, 100) + '...');
            
            try {
                // Try different removal methods
                if (header_obj.remove) {
                    header_obj.remove(header.name);
                } else if (header_obj.remove_header) {
                    header_obj.remove_header(header.name);
                } else if (header_obj.headers && header_obj.headers[header.name]) {
                    delete header_obj.headers[header.name];
                }
                headers_stripped++;
            } catch (err) {
                plugin.logwarn('Failed to remove header ' + header.name + ': ' + err.message);
            }
        }
    }
    
    if (headers_stripped > 0) {
        plugin.loginfo('=== STRIPPED ' + headers_stripped + ' HEADERS CONTAINING IP ===');
    } else {
        plugin.loginfo('=== NO HEADERS STRIPPED (no IP found) ===');
    }
    
    next();
}
