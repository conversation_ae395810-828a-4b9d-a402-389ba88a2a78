// Strip Relay Headers Plugin for Haraka
// Removes headers that reveal the home server IP address when relaying mail

const dns = require('node:dns');

exports.register = function () {
    this.load_config();
    
    // Register hooks for header manipulation
    this.register_hook('data_post', 'strip_headers');
}

exports.load_config = function () {
    this.cfg = this.config.get('strip_relay_headers.ini', {
        booleans: [
            '+main.enabled',
            '+main.strip_received_headers',
            '+main.strip_x_originating_ip',
            '+main.strip_x_forwarded_for',
            '+main.strip_x_real_ip',
            '+main.strip_custom_headers',
            '+main.replace_received_headers'
        ]
    }, () => {
        this.load_config();
    });
    
    // Set defaults
    if (!this.cfg.main) this.cfg.main = {};
    if (this.cfg.main.enabled === undefined) this.cfg.main.enabled = true;
    if (this.cfg.main.strip_received_headers === undefined) this.cfg.main.strip_received_headers = true;
    if (this.cfg.main.strip_x_originating_ip === undefined) this.cfg.main.strip_x_originating_ip = true;
    if (this.cfg.main.strip_x_forwarded_for === undefined) this.cfg.main.strip_x_forwarded_for = true;
    if (this.cfg.main.strip_x_real_ip === undefined) this.cfg.main.strip_x_real_ip = true;
    if (this.cfg.main.strip_custom_headers === undefined) this.cfg.main.strip_custom_headers = true;
    if (this.cfg.main.replace_received_headers === undefined) this.cfg.main.replace_received_headers = true;
    
    if (!this.cfg.main.relay_hostname) this.cfg.main.relay_hostname = 'hallocktest6.tmv.co.il';
    if (!this.cfg.main.relay_domain) this.cfg.main.relay_domain = 'mosh.wtf';
    
    // Headers to strip (configurable)
    if (!this.cfg.headers_to_strip) this.cfg.headers_to_strip = {};
    
    // Get current relay IP for comparison
    this.relay_ip = null;
    this.get_relay_ip();
}

exports.get_relay_ip = function () {
    const hostname = this.cfg.main.relay_hostname;
    
    dns.resolve4(hostname, (err, addresses) => {
        if (err) {
            this.logwarn(`Could not resolve ${hostname}: ${err.message}`);
            return;
        }
        
        if (addresses && addresses.length > 0) {
            this.relay_ip = addresses[0];
            this.logdebug(`Relay IP resolved to: ${this.relay_ip}`);
        }
    });
}

exports.strip_headers = function (next, connection) {
    if (!this.cfg.main.enabled) {
        return next();
    }
    
    const txn = connection?.transaction;
    if (!txn) return next();
    
    // Only process relayed mail (mail coming from our home server)
    if (!connection.relaying) {
        return next();
    }
    
    this.logdebug('Processing headers for relayed mail');
    
    let headers_modified = false;
    
    // Get all headers
    const header_lines = txn.header.header_list || [];
    
    // Track headers to remove
    const headers_to_remove = [];
    
    for (let i = 0; i < header_lines.length; i++) {
        const header = header_lines[i];
        const header_name = header.name.toLowerCase();
        const header_value = header.value;
        
        let should_remove = false;
        let reason = '';
        
        // Check Received headers for our IP
        if (header_name === 'received' && this.cfg.main.strip_received_headers) {
            if (this.contains_relay_ip(header_value)) {
                should_remove = true;
                reason = 'contains relay IP';
            }
        }
        
        // Check X-Originating-IP headers
        if (header_name === 'x-originating-ip' && this.cfg.main.strip_x_originating_ip) {
            if (this.contains_relay_ip(header_value)) {
                should_remove = true;
                reason = 'X-Originating-IP with relay IP';
            }
        }
        
        // Check X-Forwarded-For headers
        if (header_name === 'x-forwarded-for' && this.cfg.main.strip_x_forwarded_for) {
            if (this.contains_relay_ip(header_value)) {
                should_remove = true;
                reason = 'X-Forwarded-For with relay IP';
            }
        }
        
        // Check X-Real-IP headers
        if (header_name === 'x-real-ip' && this.cfg.main.strip_x_real_ip) {
            if (this.contains_relay_ip(header_value)) {
                should_remove = true;
                reason = 'X-Real-IP with relay IP';
            }
        }
        
        // Check custom headers from config
        if (this.cfg.main.strip_custom_headers && this.cfg.headers_to_strip) {
            for (const strip_header in this.cfg.headers_to_strip) {
                if (header_name === strip_header.toLowerCase()) {
                    should_remove = true;
                    reason = `custom header: ${strip_header}`;
                    break;
                }
            }
        }
        
        if (should_remove) {
            headers_to_remove.push({index: i, name: header_name, reason: reason});
            this.loginfo(`Removing header: ${header_name} (${reason})`);
            headers_modified = true;
        }
    }
    
    // Remove headers in reverse order to maintain indices
    for (let i = headers_to_remove.length - 1; i >= 0; i--) {
        const header_info = headers_to_remove[i];
        txn.header.remove_header(header_info.name);
    }
    
    // Add replacement Received header if configured
    if (this.cfg.main.replace_received_headers && headers_modified) {
        this.add_clean_received_header(txn);
    }
    
    if (headers_modified) {
        this.loginfo(`Stripped ${headers_to_remove.length} headers revealing relay IP`);
    }
    
    next();
}

exports.contains_relay_ip = function (header_value) {
    if (!this.relay_ip || !header_value) return false;
    
    // Check for exact IP match
    if (header_value.includes(this.relay_ip)) {
        return true;
    }
    
    // Check for hostname match
    if (header_value.includes(this.cfg.main.relay_hostname)) {
        return true;
    }
    
    // Check for any private IP ranges that might be our home network
    const private_ip_patterns = [
        /\b192\.168\.\d+\.\d+\b/,
        /\b10\.\d+\.\d+\.\d+\b/,
        /\b172\.(1[6-9]|2[0-9]|3[01])\.\d+\.\d+\b/
    ];
    
    for (const pattern of private_ip_patterns) {
        if (pattern.test(header_value)) {
            this.logdebug(`Found private IP in header: ${header_value}`);
            return true;
        }
    }
    
    return false;
}

exports.add_clean_received_header = function (txn) {
    // Add a clean Received header that doesn't reveal the source
    const timestamp = new Date().toUTCString();
    const relay_domain = this.cfg.main.relay_domain;
    
    const clean_received = `from ${relay_domain} (${relay_domain}) by ${relay_domain} with ESMTP; ${timestamp}`;
    
    txn.add_header('Received', clean_received);
    this.logdebug(`Added clean Received header: ${clean_received}`);
}

exports.shutdown = function () {
    this.loginfo('Strip Relay Headers plugin shutting down');
}
