// Strip Relay Headers Plugin for Haraka
// Removes headers that reveal the home server IP address when relaying mail

const dns = require('dns');

exports.register = function () {
    const plugin = this;

    plugin.load_config();

    // Register hooks for header manipulation - run at queue time to catch all headers
    plugin.register_hook('queue', 'strip_headers');
    plugin.register_hook('queue_outbound', 'strip_headers');
}

exports.load_config = function () {
    const plugin = this;

    plugin.cfg = plugin.config.get('strip_relay_headers.ini', {
        booleans: [
            '+main.enabled',
            '+main.strip_received_headers',
            '+main.strip_x_originating_ip',
            '+main.strip_x_forwarded_for',
            '+main.strip_x_real_ip',
            '+main.strip_received_spf',
            '+main.strip_authentication_results',
            '+main.strip_custom_headers',
            '+main.replace_received_headers',
            '+main.aggressive_mode'
        ]
    }, function() {
        plugin.load_config();
    });
    
    // Set defaults
    if (!this.cfg.main) this.cfg.main = {};
    if (this.cfg.main.enabled === undefined) this.cfg.main.enabled = true;
    if (this.cfg.main.strip_received_headers === undefined) this.cfg.main.strip_received_headers = true;
    if (this.cfg.main.strip_x_originating_ip === undefined) this.cfg.main.strip_x_originating_ip = true;
    if (this.cfg.main.strip_x_forwarded_for === undefined) this.cfg.main.strip_x_forwarded_for = true;
    if (this.cfg.main.strip_x_real_ip === undefined) this.cfg.main.strip_x_real_ip = true;
    if (this.cfg.main.strip_received_spf === undefined) this.cfg.main.strip_received_spf = true;
    if (this.cfg.main.strip_authentication_results === undefined) this.cfg.main.strip_authentication_results = true;
    if (this.cfg.main.strip_custom_headers === undefined) this.cfg.main.strip_custom_headers = true;
    if (this.cfg.main.replace_received_headers === undefined) this.cfg.main.replace_received_headers = true;
    if (this.cfg.main.aggressive_mode === undefined) this.cfg.main.aggressive_mode = true;
    
    if (!this.cfg.main.relay_hostname) this.cfg.main.relay_hostname = 'hallocktest6.tmv.co.il';
    if (!this.cfg.main.relay_domain) this.cfg.main.relay_domain = 'mosh.wtf';
    
    // Headers to strip (configurable)
    if (!this.cfg.headers_to_strip) this.cfg.headers_to_strip = {};
    
    // Get current relay IP for comparison
    this.relay_ip = null;
    this.get_relay_ip();
}

exports.get_relay_ip = function () {
    const hostname = this.cfg.main.relay_hostname;
    
    dns.resolve4(hostname, (err, addresses) => {
        if (err) {
            this.logwarn(`Could not resolve ${hostname}: ${err.message}`);
            return;
        }
        
        if (addresses && addresses.length > 0) {
            this.relay_ip = addresses[0];
            this.logdebug(`Relay IP resolved to: ${this.relay_ip}`);
        }
    });
}

exports.strip_headers = function (next, connection) {
    if (!this.cfg.main.enabled) {
        return next();
    }

    const txn = connection?.transaction;
    if (!txn) return next();

    // Only process relayed mail (mail coming from our home server)
    if (!connection.relaying) {
        this.logdebug('Skipping header stripping - not a relayed email');
        return next();
    }

    // Additional check: only process mail from our specific home server
    const remote_ip = connection.remote?.ip;
    this.loginfo('Header stripping check - Remote IP: ' + remote_ip + ', Relay IP: ' + this.relay_ip + ', Relaying: ' + connection.relaying);

    if (!remote_ip || !this.relay_ip || remote_ip !== this.relay_ip) {
        this.loginfo('Skipping header stripping - not from home server IP: ' + remote_ip);
        return next();
    }

    this.loginfo('Processing headers for relayed mail from home server: ' + remote_ip);

    let headers_modified = false;

    // Get all headers - use the correct method for Haraka
    const headers_to_remove = [];

    // Iterate through headers using Haraka's header methods
    const header_obj = txn.header;
    if (!header_obj) {
        this.logwarn('No header object found in transaction');
        return next();
    }

    // Get header names to iterate through
    const header_names = header_obj.get_all_headers ? Object.keys(header_obj.headers || {}) : [];

    for (const header_name_raw of header_names) {
        if (!header_name_raw) continue;

        const header_name = header_name_raw.toLowerCase();
        const header_values = header_obj.get(header_name_raw);

        if (!header_values) continue;

        // Handle both single values and arrays
        const values_array = Array.isArray(header_values) ? header_values : [header_values];

        for (const header_value of values_array) {
            if (!header_value) continue;

            let should_remove = false;
            let reason = '';

            // Aggressive mode: strip ANY header containing relay IP
            if (this.cfg.main.aggressive_mode && this.contains_relay_ip(header_value)) {
                should_remove = true;
                reason = 'aggressive mode - contains relay IP';
            }

            // Specific header checks (only if not already marked for removal)
            if (!should_remove) {
                // Check Received headers for our IP
                if (header_name === 'received' && this.cfg.main.strip_received_headers) {
                    if (this.contains_relay_ip(header_value)) {
                        should_remove = true;
                        reason = 'contains relay IP';
                    }
                }

            // Check X-Originating-IP headers
            if (header_name === 'x-originating-ip' && this.cfg.main.strip_x_originating_ip) {
                if (this.contains_relay_ip(header_value)) {
                    should_remove = true;
                    reason = 'X-Originating-IP with relay IP';
                }
            }

            // Check X-Forwarded-For headers
            if (header_name === 'x-forwarded-for' && this.cfg.main.strip_x_forwarded_for) {
                if (this.contains_relay_ip(header_value)) {
                    should_remove = true;
                    reason = 'X-Forwarded-For with relay IP';
                }
            }

            // Check X-Real-IP headers
            if (header_name === 'x-real-ip' && this.cfg.main.strip_x_real_ip) {
                if (this.contains_relay_ip(header_value)) {
                    should_remove = true;
                    reason = 'X-Real-IP with relay IP';
                }
            }

            // Check Received-SPF headers (these are major leakers!)
            if (header_name === 'received-spf' && this.cfg.main.strip_received_spf) {
                if (this.contains_relay_ip(header_value)) {
                    should_remove = true;
                    reason = 'Received-SPF with relay IP';
                }
            }

            // Check Authentication-Results headers
            if (header_name === 'authentication-results' && this.cfg.main.strip_authentication_results) {
                if (this.contains_relay_ip(header_value)) {
                    should_remove = true;
                    reason = 'Authentication-Results with relay IP';
                }
            }
            } // End of specific header checks

            // Check custom headers from config (always check these)
            if (this.cfg.main.strip_custom_headers && this.cfg.headers_to_strip) {
                for (const strip_header in this.cfg.headers_to_strip) {
                    if (header_name === strip_header.toLowerCase()) {
                        should_remove = true;
                        reason = 'custom header: ' + strip_header;
                        break;
                    }
                }
            }

            if (should_remove) {
                headers_to_remove.push({name: header_name_raw, reason: reason});
                this.loginfo('Marking header for removal: ' + header_name + ' (' + reason + ')');
                headers_modified = true;
            }
        }
    }
    
    // Remove headers
    for (const header_info of headers_to_remove) {
        try {
            header_obj.remove(header_info.name);
            this.loginfo('Removed header: ' + header_info.name + ' (' + header_info.reason + ')');
        } catch (err) {
            this.logwarn('Failed to remove header ' + header_info.name + ': ' + err.message);
        }
    }
    
    // Add replacement Received header if configured
    if (this.cfg.main.replace_received_headers && headers_modified) {
        this.add_clean_received_header(txn);
    }
    
    if (headers_modified) {
        this.loginfo(`Stripped ${headers_to_remove.length} headers revealing relay IP`);
    }
    
    next();
}

exports.contains_relay_ip = function (header_value) {
    if (!this.relay_ip || !header_value) return false;
    
    // Check for exact IP match
    if (header_value.includes(this.relay_ip)) {
        return true;
    }
    
    // Check for hostname match
    if (header_value.includes(this.cfg.main.relay_hostname)) {
        return true;
    }
    
    // Check for any private IP ranges that might be our home network
    const private_ip_patterns = [
        /\b192\.168\.\d+\.\d+\b/,
        /\b10\.\d+\.\d+\.\d+\b/,
        /\b172\.(1[6-9]|2[0-9]|3[01])\.\d+\.\d+\b/
    ];
    
    for (let i = 0; i < private_ip_patterns.length; i++) {
        const pattern = private_ip_patterns[i];
        if (pattern.test(header_value)) {
            this.logdebug('Found private IP in header: ' + header_value);
            return true;
        }
    }
    
    return false;
}

exports.add_clean_received_header = function (txn) {
    // Add a clean Received header that doesn't reveal the source
    const timestamp = new Date().toUTCString();
    const relay_domain = this.cfg.main.relay_domain;

    const clean_received = 'from ' + relay_domain + ' (' + relay_domain + ') by ' + relay_domain + ' with ESMTP; ' + timestamp;

    txn.add_header('Received', clean_received);
    this.logdebug('Added clean Received header: ' + clean_received);
}

exports.shutdown = function () {
    this.loginfo('Strip Relay Headers plugin shutting down');
}
