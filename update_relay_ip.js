#!/usr/bin/env node

// Manual IP update script for Haraka relay ACL
// This script can be run manually or via cron to update the relay ACL immediately

const dns = require('node:dns');
const fs = require('node:fs');
const path = require('node:path');

// Configuration
const hostname = 'hallocktest6.tmv.co.il';
const config_dir = './config';
const acl_file = path.join(config_dir, 'relay_acl_allow');
const backup_enabled = true;

function log(message) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${message}`);
}

function error(message) {
    const timestamp = new Date().toISOString();
    console.error(`[${timestamp}] ERROR: ${message}`);
}

function backup_config_file(file_path) {
    if (!backup_enabled) return;
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backup_path = `${file_path}.backup.${timestamp}`;
    
    try {
        fs.copyFileSync(file_path, backup_path);
        log(`Backed up ${file_path} to ${backup_path}`);
    } catch (err) {
        error(`Failed to backup ${file_path}: ${err.message}`);
    }
}

function update_relay_acl(new_ip) {
    const new_cidr = `${new_ip}/32`;
    
    // Backup existing file
    if (fs.existsSync(acl_file)) {
        backup_config_file(acl_file);
    }
    
    // Create new content
    const header = `# Only allow ${hostname} to relay through this server\n# Auto-updated by update_relay_ip.js script\n`;
    const content = header + new_cidr + '\n';
    
    try {
        fs.writeFileSync(acl_file, content, 'utf8');
        log(`Successfully updated ${acl_file} with new IP: ${new_ip}`);
        return true;
    } catch (err) {
        error(`Failed to update ${acl_file}: ${err.message}`);
        return false;
    }
}

function main() {
    log(`Starting manual IP update for ${hostname}`);
    
    dns.resolve4(hostname, (err, addresses) => {
        if (err) {
            error(`Failed to resolve ${hostname}: ${err.message}`);
            process.exit(1);
        }
        
        if (!addresses || addresses.length === 0) {
            error(`No IP addresses found for ${hostname}`);
            process.exit(1);
        }
        
        const new_ip = addresses[0];
        log(`Resolved ${hostname} to: ${new_ip}`);
        
        // Check if current config already has this IP
        if (fs.existsSync(acl_file)) {
            try {
                const current_content = fs.readFileSync(acl_file, 'utf8');
                const lines = current_content.split('\n').map(line => line.trim()).filter(line => line && !line.startsWith('#'));
                const expected_cidr = `${new_ip}/32`;
                
                if (lines.includes(expected_cidr)) {
                    log(`IP ${new_ip} is already configured in relay ACL - no update needed`);
                    process.exit(0);
                }
            } catch (err) {
                error(`Could not read current ACL file: ${err.message}`);
            }
        }
        
        // Update the ACL file
        if (update_relay_acl(new_ip)) {
            log('IP update completed successfully');
            log('Note: You may need to restart Haraka or reload the relay plugin for changes to take effect');
            process.exit(0);
        } else {
            error('IP update failed');
            process.exit(1);
        }
    });
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log('Manual IP Update Script for Haraka Relay ACL');
    console.log('');
    console.log('Usage: node update_relay_ip.js [options]');
    console.log('');
    console.log('Options:');
    console.log('  --help, -h     Show this help message');
    console.log('  --no-backup    Disable backup of existing ACL file');
    console.log('');
    console.log('This script resolves the IP address of hallocktest6.tmv.co.il');
    console.log('and updates the Haraka relay ACL configuration accordingly.');
    process.exit(0);
}

if (process.argv.includes('--no-backup')) {
    backup_enabled = false;
    log('Backup disabled by command line option');
}

main();
